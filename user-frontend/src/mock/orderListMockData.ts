// 订单列表模拟数据
export interface MockOrderItem {
  id: number
  order_no: string
  customer_order_no?: string
  tracking_no?: string
  status: string
  express_type: string
  express_name: string
  provider: string
  weight: number
  price: number
  actual_fee?: number
  insurance_fee?: number
  order_volume?: number
  actual_weight?: number
  actual_volume?: number
  charged_weight?: number
  sender_info: string
  receiver_info: string
  package_info: string
  created_at: string
  updated_at: string
  pickup_start_time?: string
  pickup_end_time?: string
  courier_name?: string
  courier_phone?: string
  pickup_code?: string
}

// 生成随机订单号
const generateOrderNo = (index: number) => {
  const timestamp = Date.now().toString().slice(-8)
  return `PO${timestamp}${String(index).padStart(4, '0')}`
}

// 生成随机运单号
const generateTrackingNo = () => {
  const prefixes = ['SF', 'YTO', 'ZTO', 'STO', 'YD', 'JD', 'DBL']
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
  const number = Math.random().toString().slice(2, 14)
  return `${prefix}${number}`
}

// 订单状态选项
const orderStatuses = [
  'pending',
  'accepted', 
  'picked_up',
  'in_transit',
  'delivered',
  'cancelled',
  'failed'
]

// 快递公司信息
const expressCompanies = [
  { code: 'SF', name: '顺丰速运' },
  { code: 'YTO', name: '圆通速递' },
  { code: 'ZTO', name: '中通快递' },
  { code: 'STO', name: '申通快递' },
  { code: 'YD', name: '韵达速递' },
  { code: 'JD', name: '京东快递' },
  { code: 'DBL', name: '德邦快递' }
]

// 供应商信息
const providers = ['cainiao', 'jd', 'dbl']

// 城市列表
const cities = [
  '北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市',
  '西安市', '重庆市', '天津市', '苏州市', '长沙市', '郑州市', '青岛市', '大连市'
]

// 姓名列表
const names = [
  '张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十',
  '陈明', '刘华', '杨丽', '黄强', '朱敏', '林峰', '何静', '郭勇'
]

// 生成随机手机号
const generatePhone = () => {
  const prefixes = ['138', '139', '150', '151', '188', '189']
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
  const suffix = Math.random().toString().slice(2, 10)
  return `${prefix}${suffix}`
}

// 生成地址信息
const generateAddressInfo = () => {
  const city = cities[Math.floor(Math.random() * cities.length)]
  const name = names[Math.floor(Math.random() * names.length)]
  const phone = generatePhone()
  const district = ['朝阳区', '海淀区', '西城区', '东城区', '丰台区', '石景山区'][Math.floor(Math.random() * 6)]
  const street = ['中关村大街', '王府井大街', '长安街', '建国门外大街', '复兴路', '西单北大街'][Math.floor(Math.random() * 6)]
  const number = Math.floor(Math.random() * 999) + 1
  
  return {
    name,
    mobile: phone,
    province: city.includes('市') ? city.replace('市', '省') : city + '省',
    city: city,
    district: district,
    address: `${district}${street}${number}号`,
    fullAddress: `${city}${district}${street}${number}号`
  }
}

// 生成包裹信息
const generatePackageInfo = () => {
  const goods = ['文件', '服装', '电子产品', '书籍', '食品', '化妆品', '玩具', '日用品']
  const goodsName = goods[Math.floor(Math.random() * goods.length)]
  
  return {
    goods_name: goodsName,
    quantity: Math.floor(Math.random() * 5) + 1,
    remark: `${goodsName}包裹，请小心轻放`
  }
}

// 生成预约时间
const generatePickupTime = () => {
  const now = new Date()
  const startTime = new Date(now.getTime() + Math.random() * 24 * 60 * 60 * 1000) // 未来24小时内
  const endTime = new Date(startTime.getTime() + 2 * 60 * 60 * 1000) // 开始时间后2小时
  
  return {
    pickup_start_time: startTime.toISOString(),
    pickup_end_time: endTime.toISOString()
  }
}

// 生成单个订单数据
const generateOrderItem = (index: number): MockOrderItem => {
  const expressCompany = expressCompanies[Math.floor(Math.random() * expressCompanies.length)]
  const provider = providers[Math.floor(Math.random() * providers.length)]
  const status = orderStatuses[Math.floor(Math.random() * orderStatuses.length)]
  const senderInfo = generateAddressInfo()
  const receiverInfo = generateAddressInfo()
  const packageInfo = generatePackageInfo()
  const pickupTime = Math.random() > 0.3 ? generatePickupTime() : {}
  
  const weight = Math.random() * 10 + 0.5 // 0.5-10.5kg
  const volume = Math.random() * 0.1 + 0.001 // 0.001-0.101m³
  const basePrice = weight * (Math.random() * 5 + 8) // 8-13元/kg
  const actualFee = basePrice + (Math.random() - 0.5) * 5 // 价格浮动
  
  const createdAt = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // 过去30天内
  
  return {
    id: index,
    order_no: generateOrderNo(index),
    customer_order_no: Math.random() > 0.3 ? `CUS${Date.now().toString().slice(-6)}${index}` : undefined,
    tracking_no: status !== 'pending' && status !== 'failed' ? generateTrackingNo() : undefined,
    status,
    express_type: expressCompany.code,
    express_name: expressCompany.name,
    provider,
    weight: Math.round(weight * 100) / 100,
    price: Math.round(basePrice * 100) / 100,
    actual_fee: Math.round(actualFee * 100) / 100,
    insurance_fee: Math.random() > 0.7 ? Math.round(Math.random() * 10 * 100) / 100 : 0,
    order_volume: Math.round(volume * 10000) / 10000,
    actual_weight: Math.round((weight + (Math.random() - 0.5) * 0.5) * 100) / 100,
    actual_volume: Math.round((volume + (Math.random() - 0.5) * 0.01) * 10000) / 10000,
    charged_weight: Math.round(Math.max(weight, volume * 8000) * 100) / 100, // 抛比8000
    sender_info: JSON.stringify(senderInfo),
    receiver_info: JSON.stringify(receiverInfo),
    package_info: JSON.stringify(packageInfo),
    created_at: createdAt.toISOString(),
    updated_at: new Date(createdAt.getTime() + Math.random() * 24 * 60 * 60 * 1000).toISOString(),
    ...pickupTime,
    courier_name: status === 'picked_up' || status === 'in_transit' || status === 'delivered' ? 
      names[Math.floor(Math.random() * names.length)] : undefined,
    courier_phone: status === 'picked_up' || status === 'in_transit' || status === 'delivered' ? 
      generatePhone() : undefined,
    pickup_code: status === 'accepted' || status === 'picked_up' ? 
      Math.random().toString(36).substr(2, 6).toUpperCase() : undefined
  }
}

// 生成订单列表
export const generateMockOrderList = (count: number = 50): MockOrderItem[] => {
  return Array.from({ length: count }, (_, index) => generateOrderItem(index + 1))
}

// 生成统计数据
export const generateMockStatistics = (orders: MockOrderItem[]) => {
  const total = orders.length
  const today = new Date().toDateString()
  const todayOrders = orders.filter(order => new Date(order.created_at).toDateString() === today)
  
  const statusCounts = orders.reduce((acc, order) => {
    acc[order.status] = (acc[order.status] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  const totalRevenue = orders.reduce((sum, order) => sum + (order.actual_fee || order.price), 0)
  const todayRevenue = todayOrders.reduce((sum, order) => sum + (order.actual_fee || order.price), 0)
  
  return {
    total,
    todayNew: todayOrders.length,
    success: statusCounts.delivered || 0,
    successCount: statusCounts.delivered || 0,
    successRate: total > 0 ? Math.round((statusCounts.delivered || 0) / total * 100) : 0,
    processing: (statusCounts.accepted || 0) + (statusCounts.picked_up || 0) + (statusCounts.in_transit || 0),
    pending: statusCounts.pending || 0,
    completed: statusCounts.delivered || 0,
    failed: statusCounts.failed || 0,
    exception: (statusCounts.failed || 0) + (statusCounts.cancelled || 0),
    exceptionCount: (statusCounts.failed || 0) + (statusCounts.cancelled || 0),
    pendingException: statusCounts.failed || 0,
    totalAmount: Math.round(totalRevenue * 100) / 100,
    totalRevenue: Math.round(totalRevenue * 100) / 100,
    todayRevenue: Math.round(todayRevenue * 100) / 100
  }
}

// 默认导出模拟数据
export const mockOrderList = generateMockOrderList(50)
export const mockStatistics = generateMockStatistics(mockOrderList)
