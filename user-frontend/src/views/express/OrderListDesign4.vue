<!-- 设计方案4：仪表板式布局 - 数据可视化，适合管理层查看 -->
<template>
  <div class="dashboard-order-list">
    <!-- 顶部仪表板 -->
    <div class="dashboard-header">
      <div class="header-title">
        <h1>订单管理仪表板</h1>
        <p>实时监控订单状态和业务指标</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建订单
        </el-button>
        <el-button @click="refreshDashboard">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="exportReport">
          <el-icon><Download /></el-icon>
          导出报表
        </el-button>
      </div>
    </div>

    <!-- 关键指标卡片 -->
    <div class="metrics-grid">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="metric-card primary">
            <div class="metric-header">
              <div class="metric-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="metric-trend up">
                <el-icon><TrendCharts /></el-icon>
                <span>+12%</span>
              </div>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ statistics.total || 0 }}</div>
              <div class="metric-label">总订单数</div>
              <div class="metric-subtitle">今日新增 {{ statistics.todayNew || 0 }} 个</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card success">
            <div class="metric-header">
              <div class="metric-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="metric-trend up">
                <el-icon><TrendCharts /></el-icon>
                <span>+8%</span>
              </div>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ statistics.successRate || 0 }}%</div>
              <div class="metric-label">成功率</div>
              <div class="metric-subtitle">{{ statistics.successCount || 0 }} / {{ statistics.total || 0 }}</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card warning">
            <div class="metric-header">
              <div class="metric-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="metric-trend up">
                <el-icon><TrendCharts /></el-icon>
                <span>+15%</span>
              </div>
            </div>
            <div class="metric-content">
              <div class="metric-number">¥{{ formatPrice(statistics.totalRevenue || 0) }}</div>
              <div class="metric-label">总收入</div>
              <div class="metric-subtitle">今日 ¥{{ formatPrice(statistics.todayRevenue || 0) }}</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card danger">
            <div class="metric-header">
              <div class="metric-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="metric-trend down">
                <el-icon><TrendCharts /></el-icon>
                <span>-3%</span>
              </div>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ statistics.exceptionCount || 0 }}</div>
              <div class="metric-label">异常订单</div>
              <div class="metric-subtitle">需要处理 {{ statistics.pendingException || 0 }} 个</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表和筛选区域 -->
    <el-row :gutter="20" class="charts-section">
      <!-- 左侧：订单趋势图 -->
      <el-col :span="16">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>订单趋势分析</span>
              <el-radio-group v-model="chartTimeRange" size="small">
                <el-radio-button label="7d">7天</el-radio-button>
                <el-radio-button label="30d">30天</el-radio-button>
                <el-radio-button label="90d">90天</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container" ref="trendChartRef"></div>
        </el-card>
      </el-col>

      <!-- 右侧：状态分布饼图 -->
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>订单状态分布</span>
          </template>
          <div class="chart-container" ref="statusChartRef"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 智能筛选面板 -->
    <el-card class="filter-panel">
      <template #header>
        <div class="filter-header">
          <span>智能筛选</span>
          <el-button size="small" @click="resetFilters">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </div>
      </template>
      
      <div class="filter-content">
        <!-- 快速筛选标签 -->
        <div class="quick-filters">
          <el-tag
            v-for="filter in quickFilters"
            :key="filter.key"
            :type="filter.active ? 'primary' : ''"
            :effect="filter.active ? 'dark' : 'plain'"
            class="filter-tag"
            @click="toggleQuickFilter(filter)"
          >
            <el-icon>
              <component :is="filter.icon" />
            </el-icon>
            {{ filter.label }}
            <span v-if="filter.count" class="filter-count">({{ filter.count }})</span>
          </el-tag>
        </div>

        <!-- 高级筛选 -->
        <el-form :model="filterForm" inline class="advanced-filters">
          <el-form-item label="搜索">
            <el-input
              v-model="filterForm.keyword"
              placeholder="订单号、运单号、收件人..."
              clearable
              style="width: 250px;"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="filterForm.status" placeholder="选择状态" clearable multiple>
              <el-option
                v-for="status in orderStatusOptions"
                :key="status.value"
                :label="status.label"
                :value="status.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="快递公司">
            <el-select v-model="filterForm.expressType" placeholder="选择快递公司" clearable>
              <el-option
                v-for="company in expressCompanyOptions"
                :key="company.value"
                :label="company.label"
                :value="company.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="供应商">
            <el-select v-model="filterForm.provider" placeholder="选择供应商" clearable>
              <el-option label="菜鸟" value="cainiao" />
              <el-option label="京东" value="jd" />
              <el-option label="德邦" value="dbl" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item label="金额范围">
            <el-slider
              v-model="filterForm.priceRange"
              range
              :min="0"
              :max="1000"
              :step="10"
              show-input
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="applyFilters">
              <el-icon><Search /></el-icon>
              应用筛选
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 订单网格视图 -->
    <el-card class="orders-grid">
      <template #header>
        <div class="grid-header">
          <div class="grid-title">
            <span>订单列表</span>
            <el-tag type="info">共 {{ pagination.total }} 个订单</el-tag>
          </div>
          <div class="grid-actions">
            <el-button-group>
              <el-button :type="viewMode === 'grid' ? 'primary' : ''" @click="viewMode = 'grid'">
                <el-icon><Grid /></el-icon>
                网格
              </el-button>
              <el-button :type="viewMode === 'list' ? 'primary' : ''" @click="viewMode = 'list'">
                <el-icon><List /></el-icon>
                列表
              </el-button>
            </el-button-group>
            <el-select v-model="sortBy" placeholder="排序方式" style="width: 150px;">
              <el-option label="创建时间" value="created_at" />
              <el-option label="更新时间" value="updated_at" />
              <el-option label="金额" value="amount" />
              <el-option label="状态" value="status" />
            </el-select>
          </div>
        </div>
      </template>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="grid-view" v-loading="loading">
        <div class="order-grid">
          <div
            v-for="order in orderList"
            :key="order.order_no"
            class="order-grid-item"
            @click="viewOrderDetail(order)"
          >
            <!-- 订单卡片 -->
            <div class="order-card">
              <!-- 卡片头部 -->
              <div class="card-header">
                <div class="order-info">
                  <div class="order-number">{{ order.order_no }}</div>
                  <div class="order-time">{{ formatRelativeTime(order.created_at) }}</div>
                </div>
                <div class="order-status">
                  <el-tag :type="getStatusType(order.status)" size="small">
                    {{ getStatusDesc(order.status) }}
                  </el-tag>
                </div>
              </div>

              <!-- 卡片内容 -->
              <div class="card-content">
                <div class="express-info">
                  <div class="express-name">{{ getExpressName(order) }}</div>
                  <el-tag :type="getProviderTagType(order.provider)" size="small">
                    {{ getProviderName(order) }}
                  </el-tag>
                </div>
                
                <div class="address-summary">
                  <div class="address-item">
                    <el-icon><User /></el-icon>
                    <span>{{ getSenderInfo(order).city }}</span>
                  </div>
                  <div class="address-arrow">
                    <el-icon><Right /></el-icon>
                  </div>
                  <div class="address-item">
                    <el-icon><UserFilled /></el-icon>
                    <span>{{ getReceiverInfo(order).city }}</span>
                  </div>
                </div>

                <div class="order-metrics">
                  <div class="metric-item">
                    <span class="label">重量</span>
                    <span class="value">{{ formatWeight(order.weight) }}kg</span>
                  </div>
                  <div class="metric-item">
                    <span class="label">费用</span>
                    <span class="value price">¥{{ formatPrice(order.actual_fee || order.price || 0) }}</span>
                  </div>
                </div>
              </div>

              <!-- 卡片底部 -->
              <div class="card-footer">
                <div class="progress-indicator">
                  <div class="progress-bar">
                    <div 
                      class="progress-fill" 
                      :style="{ width: getProgressPercentage(order.status) + '%' }"
                    ></div>
                  </div>
                  <div class="progress-text">{{ getProgressText(order.status) }}</div>
                </div>
                <div class="card-actions">
                  <el-button size="small" circle @click.stop="viewOrderDetail(order)">
                    <el-icon><View /></el-icon>
                  </el-button>
                  <el-button
                    v-if="canCancelOrder(order)"
                    size="small"
                    type="danger"
                    circle
                    @click.stop="cancelOrder(order)"
                  >
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else class="list-view" v-loading="loading">
        <el-table :data="orderList" stripe>
          <el-table-column prop="order_no" label="订单号" width="140" />
          <el-table-column prop="tracking_no" label="运单号" width="140" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusDesc(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="快递信息" width="160">
            <template #default="{ row }">
              <div>{{ getExpressName(row) }}</div>
              <el-tag :type="getProviderTagType(row.provider)" size="small">
                {{ getProviderName(row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="寄收件地址" width="200">
            <template #default="{ row }">
              <div class="address-info">
                <div>{{ getSenderInfo(row).city }} → {{ getReceiverInfo(row).city }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="weight" label="重量" width="80">
            <template #default="{ row }">
              {{ formatWeight(row.weight) }}kg
            </template>
          </el-table-column>
          <el-table-column label="费用" width="100">
            <template #default="{ row }">
              ¥{{ formatPrice(row.actual_fee || row.price || 0) }}
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="viewOrderDetail(row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[12, 24, 48, 96]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus,
  Refresh,
  Download,
  Document,
  CircleCheck,
  Money,
  Warning,
  TrendCharts,
  RefreshLeft,
  Search,
  Grid,
  List,
  User,
  UserFilled,
  Right,
  View,
  Close
} from '@element-plus/icons-vue'
import { mockOrderList, mockStatistics, type MockOrderItem } from '@/mock/orderListMockData'

// 响应式数据
const loading = ref(false)
const showCreateDialog = ref(false)
const chartTimeRange = ref('7d')
const viewMode = ref('grid')
const sortBy = ref('created_at')
const trendChartRef = ref()
const statusChartRef = ref()
const orderList = ref<MockOrderItem[]>(mockOrderList)

// 统计数据
const statistics = ref(mockStatistics)

// 筛选表单
const filterForm = reactive({
  keyword: '',
  status: [],
  expressType: '',
  provider: '',
  dateRange: null,
  priceRange: [0, 1000]
})

// 分页信息
const pagination = reactive({
  page: 1,
  page_size: 24,
  total: 0
})

// 快速筛选选项
const quickFilters = ref([
  { key: 'today', label: '今日订单', icon: 'Calendar', active: false, count: 0 },
  { key: 'pending', label: '待处理', icon: 'Clock', active: false, count: 0 },
  { key: 'exception', label: '异常订单', icon: 'Warning', active: false, count: 0 },
  { key: 'high_value', label: '高价值', icon: 'Money', active: false, count: 0 },
  { key: 'urgent', label: '紧急订单', icon: 'Lightning', active: false, count: 0 }
])

// 订单状态选项
const orderStatusOptions = ref([
  { label: '待处理', value: 'pending' },
  { label: '已接单', value: 'accepted' },
  { label: '已揽件', value: 'picked_up' },
  { label: '运输中', value: 'in_transit' },
  { label: '已签收', value: 'delivered' },
  { label: '已取消', value: 'cancelled' },
  { label: '失败', value: 'failed' }
])

// 快递公司选项
const expressCompanyOptions = ref([
  { label: '顺丰速运', value: 'SF' },
  { label: '圆通速递', value: 'YTO' },
  { label: '中通快递', value: 'ZTO' },
  { label: '申通快递', value: 'STO' },
  { label: '韵达速递', value: 'YD' },
  { label: '京东快递', value: 'JD' },
  { label: '德邦快递', value: 'DBL' }
])

// 方法
const refreshDashboard = () => {
  console.log('刷新仪表板')
}

const exportReport = () => {
  console.log('导出报表')
}

const toggleQuickFilter = (filter: any) => {
  filter.active = !filter.active
}

const resetFilters = () => {
  Object.keys(filterForm).forEach(key => {
    if (Array.isArray(filterForm[key])) {
      filterForm[key] = []
    } else {
      filterForm[key] = ''
    }
  })
  filterForm.priceRange = [0, 1000]
}

const applyFilters = () => {
  console.log('应用筛选')
}

const viewOrderDetail = (order: any) => {
  console.log('查看订单详情', order)
}

const cancelOrder = (order: any) => {
  console.log('取消订单', order)
}

const handleSizeChange = (size: number) => {
  pagination.page_size = size
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
}

// 辅助方法
const formatPrice = (price: number) => {
  return price ? price.toFixed(2) : '0.00'
}

const formatWeight = (weight: number) => {
  return weight ? weight.toFixed(2) : '0.00'
}

const formatDateTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

const formatRelativeTime = (dateStr: string) => {
  const now = new Date()
  const date = new Date(dateStr)
  const diff = now.getTime() - date.getTime()
  const hours = Math.floor(diff / (1000 * 60 * 60))
  
  if (hours < 1) return '刚刚'
  if (hours < 24) return `${hours}小时前`
  const days = Math.floor(hours / 24)
  return `${days}天前`
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    accepted: 'primary',
    picked_up: 'info',
    in_transit: 'primary',
    delivered: 'success',
    cancelled: 'info',
    failed: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusDesc = (status: string) => {
  const descMap: Record<string, string> = {
    pending: '待处理',
    accepted: '已接单',
    picked_up: '已揽件',
    in_transit: '运输中',
    delivered: '已签收',
    cancelled: '已取消',
    failed: '失败'
  }
  return descMap[status] || status
}

const getSenderInfo = (order: any) => {
  try {
    return JSON.parse(order.sender_info || '{}')
  } catch {
    return { city: '-' }
  }
}

const getReceiverInfo = (order: any) => {
  try {
    return JSON.parse(order.receiver_info || '{}')
  } catch {
    return { city: '-' }
  }
}

const getExpressName = (order: any) => {
  return order.express_name || order.express_type || '-'
}

const getProviderName = (order: any) => {
  const nameMap: Record<string, string> = {
    cainiao: '菜鸟',
    jd: '京东',
    dbl: '德邦'
  }
  return nameMap[order.provider] || order.provider
}

const getProviderTagType = (provider: string) => {
  const typeMap: Record<string, string> = {
    cainiao: 'warning',
    jd: 'danger',
    dbl: 'success'
  }
  return typeMap[provider] || 'info'
}

const getProgressPercentage = (status: string) => {
  const progressMap: Record<string, number> = {
    pending: 10,
    accepted: 25,
    picked_up: 50,
    in_transit: 75,
    delivered: 100,
    cancelled: 0,
    failed: 0
  }
  return progressMap[status] || 0
}

const getProgressText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '等待处理',
    accepted: '已接单',
    picked_up: '已揽件',
    in_transit: '运输中',
    delivered: '已完成',
    cancelled: '已取消',
    failed: '处理失败'
  }
  return textMap[status] || status
}

const canCancelOrder = (order: any) => {
  return ['pending', 'accepted'].includes(order.status)
}

onMounted(() => {
  // 初始化数据已通过模拟数据加载
  console.log('订单列表设计4已加载', orderList.value.length, '个订单')
  nextTick(() => {
    // 初始化趋势图表
    // 初始化状态分布图表
  })
})
</script>

<style scoped lang="scss">
/* 仪表板式布局样式 */
.dashboard-order-list {
  padding: 20px;
  background: #f0f2f5;
  min-height: 100vh;

  /* 仪表板头部样式 */
  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .header-title {
      h1 {
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 700;
        color: #303133;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      p {
        margin: 0;
        color: #909399;
        font-size: 16px;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .el-button {
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 600;
      }
    }
  }

  /* 指标网格样式 */
  .metrics-grid {
    margin-bottom: 24px;

    .metric-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      }

      &.primary::before {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      }

      &.success::before {
        background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
      }

      &.warning::before {
        background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%);
      }

      &.danger::before {
        background: linear-gradient(90deg, #fa709a 0%, #fee140 100%);
      }

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
      }

      .metric-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .metric-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: white;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .metric-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 14px;
          font-weight: 600;

          &.up {
            color: #67c23a;
          }

          &.down {
            color: #f56c6c;
          }
        }
      }

      .metric-content {
        .metric-number {
          font-size: 32px;
          font-weight: 700;
          color: #303133;
          line-height: 1;
          margin-bottom: 8px;
        }

        .metric-label {
          font-size: 16px;
          color: #606266;
          font-weight: 600;
          margin-bottom: 4px;
        }

        .metric-subtitle {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }

  /* 图表区域样式 */
  .charts-section {
    margin-bottom: 24px;

    .chart-card {
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;
        color: #303133;
      }

      .chart-container {
        height: 300px;
        width: 100%;
      }
    }
  }

  /* 筛选面板样式 */
  .filter-panel {
    margin-bottom: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .filter-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }

    .filter-content {
      .quick-filters {
        display: flex;
        gap: 12px;
        margin-bottom: 20px;
        flex-wrap: wrap;

        .filter-tag {
          cursor: pointer;
          transition: all 0.3s ease;
          border-radius: 20px;
          padding: 8px 16px;
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 6px;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          }

          .filter-count {
            font-size: 12px;
            opacity: 0.8;
          }
        }
      }

      .advanced-filters {
        .el-form-item {
          margin-bottom: 16px;
        }
      }
    }
  }

  /* 订单网格样式 */
  .orders-grid {
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .grid-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .grid-title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-weight: 600;
        color: #303133;
      }

      .grid-actions {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }

    /* 网格视图样式 */
    .grid-view {
      .order-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 20px;
        padding: 20px 0;

        .order-grid-item {
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
          }

          .order-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
            }

            .card-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 16px 20px;
              border-bottom: 1px solid #f0f2f5;

              .order-info {
                .order-number {
                  font-size: 16px;
                  font-weight: 600;
                  color: #409eff;
                  font-family: 'SF Mono', 'Monaco', monospace;
                  margin-bottom: 4px;
                }

                .order-time {
                  font-size: 12px;
                  color: #909399;
                }
              }

              .order-status {
                .el-tag {
                  font-weight: 600;
                  border-radius: 12px;
                }
              }
            }

            .card-content {
              padding: 16px 20px;

              .express-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;

                .express-name {
                  font-weight: 600;
                  color: #303133;
                }
              }

              .address-summary {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 12px;
                padding: 8px 12px;
                background: #f8f9fa;
                border-radius: 8px;

                .address-item {
                  display: flex;
                  align-items: center;
                  gap: 4px;
                  font-size: 14px;
                  color: #606266;
                }

                .address-arrow {
                  color: #c0c4cc;
                }
              }

              .order-metrics {
                display: flex;
                justify-content: space-between;

                .metric-item {
                  text-align: center;

                  .label {
                    display: block;
                    font-size: 12px;
                    color: #909399;
                    margin-bottom: 4px;
                  }

                  .value {
                    font-weight: 600;
                    color: #303133;

                    &.price {
                      color: #f56c6c;
                      font-size: 16px;
                    }
                  }
                }
              }
            }

            .card-footer {
              padding: 16px 20px;
              background: #fafbfc;

              .progress-indicator {
                margin-bottom: 12px;

                .progress-bar {
                  height: 4px;
                  background: #e4e7ed;
                  border-radius: 2px;
                  overflow: hidden;
                  margin-bottom: 4px;

                  .progress-fill {
                    height: 100%;
                    background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
                    transition: width 0.3s ease;
                  }
                }

                .progress-text {
                  font-size: 12px;
                  color: #606266;
                  text-align: center;
                }
              }

              .card-actions {
                display: flex;
                justify-content: flex-end;
                gap: 8px;

                .el-button {
                  border-radius: 50%;
                }
              }
            }
          }
        }
      }
    }

    /* 列表视图样式 */
    .list-view {
      :deep(.el-table) {
        border-radius: 8px;
        overflow: hidden;

        .el-table__header {
          background: #f8f9fa;

          th {
            background: #f8f9fa !important;
            color: #606266;
            font-weight: 600;
          }
        }

        .el-table__row {
          &:hover {
            background: #f5f7fa !important;
          }
        }
      }

      .address-info {
        font-size: 13px;
        color: #606266;
      }
    }

    /* 分页样式 */
    .pagination-wrapper {
      display: flex;
      justify-content: center;
      padding: 24px 0;
      border-top: 1px solid #f0f2f5;
      margin-top: 20px;
    }
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .dashboard-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .metrics-grid .el-col {
      margin-bottom: 16px;
    }

    .charts-section .el-col {
      margin-bottom: 16px;
    }
  }

  @media (max-width: 768px) {
    padding: 12px;

    .dashboard-header {
      padding: 16px;

      .header-title h1 {
        font-size: 24px;
      }

      .header-actions {
        flex-direction: column;
        gap: 8px;

        .el-button {
          width: 100%;
        }
      }
    }

    .filter-panel .filter-content {
      .quick-filters {
        justify-content: center;
      }

      .advanced-filters {
        .el-form-item {
          width: 100%;
          margin-right: 0;
        }
      }
    }

    .orders-grid {
      .grid-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
      }

      .grid-view .order-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }
  }
}
</style>
