<!-- 设计方案1：现代卡片式布局 - 信息密度高，视觉层次清晰 -->
<template>
  <div class="modern-order-list">
    <!-- 顶部统计卡片 -->
    <div class="stats-overview">
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="stat-card total">
            <div class="stat-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.total || 0 }}</div>
              <div class="stat-label">总订单</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card success">
            <div class="stat-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.success || 0 }}</div>
              <div class="stat-label">成功订单</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card processing">
            <div class="stat-icon">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.processing || 0 }}</div>
              <div class="stat-label">处理中</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card failed">
            <div class="stat-icon">
              <el-icon><CircleClose /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.failed || 0 }}</div>
              <div class="stat-label">失败订单</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 智能搜索栏 -->
    <el-card class="search-card">
      <div class="smart-search">
        <div class="search-input-group">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索订单号、运单号、收件人姓名或手机号..."
            size="large"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button type="primary" @click="handleSearch">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
            </template>
          </el-input>
        </div>
        
        <!-- 快速筛选标签 -->
        <div class="quick-filters">
          <el-tag
            v-for="filter in quickFilters"
            :key="filter.key"
            :type="filter.active ? 'primary' : ''"
            :effect="filter.active ? 'dark' : 'plain'"
            class="filter-tag"
            @click="toggleQuickFilter(filter)"
          >
            {{ filter.label }}
          </el-tag>
        </div>

        <!-- 高级筛选 -->
        <el-collapse v-model="showAdvancedFilters" class="advanced-filters">
          <el-collapse-item name="advanced" title="高级筛选">
            <el-form :model="searchForm" inline>
              <el-form-item label="订单状态">
                <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
                  <el-option
                    v-for="status in orderStatusOptions"
                    :key="status.value"
                    :label="status.label"
                    :value="status.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="快递公司">
                <el-select v-model="searchForm.express_type" placeholder="选择快递公司" clearable>
                  <el-option
                    v-for="company in expressCompanyOptions"
                    :key="company.value"
                    :label="company.label"
                    :value="company.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="供应商">
                <el-select v-model="searchForm.provider" placeholder="选择供应商" clearable>
                  <el-option label="菜鸟" value="cainiao" />
                  <el-option label="京东" value="jd" />
                  <el-option label="德邦" value="dbl" />
                </el-select>
              </el-form-item>
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="dateRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>
              <el-form-item label="价格范围">
                <el-input-number v-model="searchForm.price_min" placeholder="最低价格" :min="0" />
                <span style="margin: 0 8px;">-</span>
                <el-input-number v-model="searchForm.price_max" placeholder="最高价格" :min="0" />
              </el-form-item>
            </el-form>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>

    <!-- 订单卡片列表 -->
    <div class="order-cards" v-loading="loading">
      <div
        v-for="order in orderList"
        :key="order.order_no"
        class="order-card"
        @click="viewOrderDetail(order)"
      >
        <!-- 卡片头部 -->
        <div class="card-header">
          <div class="order-info">
            <div class="order-number">
              <span class="label">订单号：</span>
              <span class="value">{{ order.order_no }}</span>
            </div>
            <div class="tracking-number" v-if="order.tracking_no">
              <span class="label">运单号：</span>
              <span class="value">{{ order.tracking_no }}</span>
            </div>
          </div>
          <div class="order-status">
            <el-tag :type="getStatusType(order.status)" size="large">
              {{ getStatusDesc(order.status) }}
            </el-tag>
          </div>
        </div>

        <!-- 卡片主体 -->
        <div class="card-body">
          <el-row :gutter="24">
            <!-- 左侧：地址信息 -->
            <el-col :span="12">
              <div class="address-section">
                <div class="address-item sender">
                  <div class="address-header">
                    <el-icon><User /></el-icon>
                    <span>寄件人</span>
                  </div>
                  <div class="address-content">
                    <div class="name-phone">
                      {{ getSenderInfo(order).name }} {{ getSenderInfo(order).mobile }}
                    </div>
                    <div class="address">{{ getSenderInfo(order).address }}</div>
                  </div>
                </div>
                <div class="address-arrow">
                  <el-icon><Right /></el-icon>
                </div>
                <div class="address-item receiver">
                  <div class="address-header">
                    <el-icon><UserFilled /></el-icon>
                    <span>收件人</span>
                  </div>
                  <div class="address-content">
                    <div class="name-phone">
                      {{ getReceiverInfo(order).name }} {{ getReceiverInfo(order).mobile }}
                    </div>
                    <div class="address">{{ getReceiverInfo(order).address }}</div>
                  </div>
                </div>
              </div>
            </el-col>

            <!-- 右侧：订单详情 -->
            <el-col :span="12">
              <div class="order-details">
                <div class="detail-row">
                  <div class="detail-item">
                    <span class="label">快递公司：</span>
                    <span class="value">{{ getExpressName(order) }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">供应商：</span>
                    <el-tag :type="getProviderTagType(order.provider)" size="small">
                      {{ getProviderName(order) }}
                    </el-tag>
                  </div>
                </div>
                <div class="detail-row">
                  <div class="detail-item">
                    <span class="label">重量：</span>
                    <span class="value">{{ formatWeight(order.weight) }}kg</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">体积：</span>
                    <span class="value">{{ formatVolume(order.order_volume) }}</span>
                  </div>
                </div>
                <div class="detail-row">
                  <div class="detail-item">
                    <span class="label">预收费用：</span>
                    <span class="value price">¥{{ formatPrice(order.price || 0) }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">实收费用：</span>
                    <span class="value price">¥{{ formatPrice(order.actual_fee || 0) }}</span>
                  </div>
                </div>
                <div class="detail-row">
                  <div class="detail-item full-width">
                    <span class="label">创建时间：</span>
                    <span class="value">{{ formatDate(order.created_at) }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 卡片底部操作 -->
        <div class="card-footer">
          <div class="pickup-info" v-if="hasPickupTime(order)">
            <el-icon><Clock /></el-icon>
            <span>预约时间：{{ formatPickupTime(order) }}</span>
          </div>
          <div class="action-buttons">
            <el-button size="small" @click.stop="viewOrderDetail(order)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button
              v-if="canCancelOrder(order)"
              type="danger"
              size="small"
              @click.stop="cancelOrder(order)"
            >
              <el-icon><Close /></el-icon>
              取消
            </el-button>
            <el-button
              v-if="canRetryOrder(order)"
              type="primary"
              size="small"
              @click.stop="retryOrder(order)"
            >
              <el-icon><RefreshLeft /></el-icon>
              重试
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.page_size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document,
  CircleCheck,
  Loading,
  CircleClose,
  Search,
  User,
  UserFilled,
  Right,
  Clock,
  View,
  Close,
  RefreshLeft
} from '@element-plus/icons-vue'
import { mockOrderList, mockStatistics, type MockOrderItem } from '@/mock/orderListMockData'

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const showAdvancedFilters = ref([])
const dateRange = ref<[string, string] | null>(null)
const orderList = ref<MockOrderItem[]>(mockOrderList)
const statistics = ref(mockStatistics)

// 搜索表单
const searchForm = reactive({
  status: undefined,
  express_type: undefined,
  provider: undefined,
  price_min: undefined,
  price_max: undefined
})

// 分页信息
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: mockOrderList.length
})

// 快速筛选选项
const quickFilters = ref([
  { key: 'today', label: '今日订单', active: false },
  { key: 'pending', label: '待处理', active: false },
  { key: 'success', label: '已成功', active: false },
  { key: 'failed', label: '已失败', active: false },
  { key: 'high_value', label: '高价值订单', active: false }
])

// 订单状态选项
const orderStatusOptions = ref([
  { label: '待处理', value: 'pending' },
  { label: '已接单', value: 'accepted' },
  { label: '已揽件', value: 'picked_up' },
  { label: '运输中', value: 'in_transit' },
  { label: '已签收', value: 'delivered' },
  { label: '已取消', value: 'cancelled' },
  { label: '失败', value: 'failed' }
])

// 快递公司选项
const expressCompanyOptions = ref([
  { label: '顺丰速运', value: 'SF' },
  { label: '圆通速递', value: 'YTO' },
  { label: '中通快递', value: 'ZTO' },
  { label: '申通快递', value: 'STO' },
  { label: '韵达速递', value: 'YD' },
  { label: '京东快递', value: 'JD' },
  { label: '德邦快递', value: 'DBL' }
])

// 方法
const handleSearch = () => {
  console.log('执行搜索')
}

const toggleQuickFilter = (filter: any) => {
  filter.active = !filter.active
}

const viewOrderDetail = (order: any) => {
  console.log('查看订单详情', order)
}

const cancelOrder = (order: any) => {
  console.log('取消订单', order)
}

const retryOrder = (order: any) => {
  console.log('重试订单', order)
}

const handleSizeChange = (size: number) => {
  pagination.page_size = size
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
}

// 辅助方法
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    accepted: 'primary',
    picked_up: 'info',
    in_transit: 'primary',
    delivered: 'success',
    cancelled: 'info',
    failed: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusDesc = (status: string) => {
  const descMap: Record<string, string> = {
    pending: '待处理',
    accepted: '已接单',
    picked_up: '已揽件',
    in_transit: '运输中',
    delivered: '已签收',
    cancelled: '已取消',
    failed: '失败'
  }
  return descMap[status] || status
}

const getSenderInfo = (order: any) => {
  try {
    return JSON.parse(order.sender_info || '{}')
  } catch {
    return { name: '-', mobile: '-', address: '-' }
  }
}

const getReceiverInfo = (order: any) => {
  try {
    return JSON.parse(order.receiver_info || '{}')
  } catch {
    return { name: '-', mobile: '-', address: '-' }
  }
}

const getExpressName = (order: any) => {
  return order.express_name || order.express_type || '-'
}

const getProviderName = (order: any) => {
  const nameMap: Record<string, string> = {
    cainiao: '菜鸟',
    jd: '京东',
    dbl: '德邦'
  }
  return nameMap[order.provider] || order.provider
}

const getProviderTagType = (provider: string) => {
  const typeMap: Record<string, string> = {
    cainiao: 'warning',
    jd: 'danger',
    dbl: 'success'
  }
  return typeMap[provider] || 'info'
}

const formatWeight = (weight: number) => {
  return weight ? weight.toFixed(2) : '0.00'
}

const formatVolume = (volume: number) => {
  return volume ? `${volume.toFixed(4)}m³` : '-'
}

const formatPrice = (price: number) => {
  return price ? price.toFixed(2) : '0.00'
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

const hasPickupTime = (order: any) => {
  return order.pickup_start_time && order.pickup_end_time
}

const formatPickupTime = (order: any) => {
  if (!hasPickupTime(order)) return '-'
  const start = new Date(order.pickup_start_time).toLocaleString('zh-CN')
  const end = new Date(order.pickup_end_time).toLocaleString('zh-CN')
  return `${start} - ${end}`
}

const canCancelOrder = (order: any) => {
  return ['pending', 'accepted'].includes(order.status)
}

const canRetryOrder = (order: any) => {
  return order.status === 'failed'
}

onMounted(() => {
  // 初始化数据已通过模拟数据加载
  console.log('订单列表设计1已加载', orderList.value.length, '个订单')
})
</script>

<style scoped lang="scss">
/* 现代卡片式布局样式 */
.modern-order-list {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;

  /* 统计卡片样式 */
  .stats-overview {
    margin-bottom: 24px;

    .stat-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 16px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
      }

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
      }

      .stat-content {
        flex: 1;

        .stat-number {
          font-size: 28px;
          font-weight: 700;
          color: #303133;
          line-height: 1;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
          font-weight: 500;
        }
      }

      &.total .stat-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.success .stat-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      &.processing .stat-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }

      &.failed .stat-icon {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      }
    }
  }

  /* 搜索卡片样式 */
  .search-card {
    margin-bottom: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .smart-search {
      .search-input-group {
        margin-bottom: 16px;

        .el-input {
          border-radius: 8px;

          :deep(.el-input__wrapper) {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 2px solid #e4e7ed;
            transition: all 0.3s ease;

            &:hover {
              border-color: #c0c4cc;
            }

            &.is-focus {
              border-color: #409eff;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
            }
          }

          :deep(.el-input-group__append) {
            border-radius: 0 8px 8px 0;
            border: none;
            background: #409eff;

            .el-button {
              border: none;
              background: transparent;
              color: white;
              font-weight: 600;

              &:hover {
                background: rgba(255, 255, 255, 0.1);
              }
            }
          }
        }
      }

      .quick-filters {
        display: flex;
        gap: 8px;
        margin-bottom: 16px;
        flex-wrap: wrap;

        .filter-tag {
          cursor: pointer;
          transition: all 0.3s ease;
          border-radius: 20px;
          padding: 8px 16px;
          font-weight: 500;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          }
        }
      }

      .advanced-filters {
        :deep(.el-collapse-item__header) {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 12px 16px;
          font-weight: 600;
          color: #606266;
        }

        :deep(.el-collapse-item__content) {
          padding: 20px 16px;
          background: #fafbfc;
          border-radius: 0 0 8px 8px;
        }
      }
    }
  }

  /* 订单卡片样式 */
  .order-cards {
    display: grid;
    gap: 16px;
    grid-template-columns: 1fr;

    .order-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      cursor: pointer;
      overflow: hidden;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px 16px;
        border-bottom: 1px solid #f0f2f5;

        .order-info {
          .order-number {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;

            .label {
              color: #909399;
              font-weight: 500;
            }

            .value {
              color: #409eff;
              font-family: 'SF Mono', 'Monaco', monospace;
            }
          }

          .tracking-number {
            font-size: 14px;
            color: #606266;

            .label {
              color: #909399;
            }

            .value {
              color: #67c23a;
              font-family: 'SF Mono', 'Monaco', monospace;
              font-weight: 600;
            }
          }
        }

        .order-status {
          .el-tag {
            font-weight: 600;
            border-radius: 20px;
            padding: 8px 16px;
          }
        }
      }

      .card-body {
        padding: 20px 24px;

        .address-section {
          display: flex;
          align-items: center;
          gap: 16px;

          .address-item {
            flex: 1;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px solid transparent;
            transition: all 0.3s ease;

            &.sender {
              border-color: #e3f2fd;
              background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
            }

            &.receiver {
              border-color: #f3e5f5;
              background: linear-gradient(135deg, #f3e5f5 0%, #f8f9fa 100%);
            }

            .address-header {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;
              font-weight: 600;
              color: #606266;
              font-size: 14px;
            }

            .address-content {
              .name-phone {
                font-weight: 600;
                color: #303133;
                margin-bottom: 4px;
              }

              .address {
                font-size: 13px;
                color: #909399;
                line-height: 1.4;
              }
            }
          }

          .address-arrow {
            color: #c0c4cc;
            font-size: 20px;
          }
        }

        .order-details {
          margin-top: 20px;

          .detail-row {
            display: flex;
            gap: 24px;
            margin-bottom: 12px;

            &:last-child {
              margin-bottom: 0;
            }

            .detail-item {
              flex: 1;
              display: flex;
              align-items: center;
              gap: 8px;

              &.full-width {
                flex: 2;
              }

              .label {
                color: #909399;
                font-size: 14px;
                font-weight: 500;
                min-width: 70px;
              }

              .value {
                color: #303133;
                font-weight: 600;

                &.price {
                  color: #f56c6c;
                  font-size: 16px;
                }
              }
            }
          }
        }
      }

      .card-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px 20px;
        background: #fafbfc;

        .pickup-info {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #67c23a;
          font-size: 14px;
          font-weight: 500;
        }

        .action-buttons {
          display: flex;
          gap: 8px;

          .el-button {
            border-radius: 6px;
            font-weight: 500;
            padding: 8px 16px;
          }
        }
      }
    }
  }

  /* 分页样式 */
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 32px;
    padding: 24px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .stats-overview .el-col {
      margin-bottom: 16px;
    }
  }

  @media (max-width: 768px) {
    padding: 12px;

    .order-cards .order-card {
      .card-body {
        .address-section {
          flex-direction: column;
          gap: 12px;

          .address-arrow {
            transform: rotate(90deg);
          }
        }

        .order-details .detail-row {
          flex-direction: column;
          gap: 8px;

          .detail-item {
            justify-content: space-between;
          }
        }
      }

      .card-footer {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;

        .action-buttons {
          justify-content: center;
        }
      }
    }
  }
}
</style>
