<!-- 设计方案3：时间线式布局 - 以时间为主线，直观展示订单流程 -->
<template>
  <div class="timeline-order-list">
    <!-- 顶部控制面板 -->
    <el-card class="control-panel">
      <div class="panel-header">
        <h2>订单时间线</h2>
        <div class="header-actions">
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            新建订单
          </el-button>
          <el-button @click="refreshTimeline">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <!-- 时间筛选器 -->
      <div class="time-selector">
        <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
          <el-radio-button label="today">今天</el-radio-button>
          <el-radio-button label="week">本周</el-radio-button>
          <el-radio-button label="month">本月</el-radio-button>
          <el-radio-button label="custom">自定义</el-radio-button>
        </el-radio-group>
        
        <el-date-picker
          v-if="timeRange === 'custom'"
          v-model="customDateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm:ss"
          @change="handleCustomDateChange"
        />
      </div>

      <!-- 快速搜索 -->
      <div class="quick-search">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索订单号、运单号、收件人..."
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #append>
            <el-button @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>

      <!-- 状态筛选 -->
      <div class="status-filters">
        <el-checkbox-group v-model="selectedStatuses" @change="handleStatusFilter">
          <el-checkbox-button
            v-for="status in orderStatusOptions"
            :key="status.value"
            :label="status.value"
            :value="status.value"
          >
            <el-tag :type="getStatusType(status.value)" size="small">
              {{ status.label }}
            </el-tag>
          </el-checkbox-button>
        </el-checkbox-group>
      </div>
    </el-card>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <el-row :gutter="16">
        <el-col :span="8">
          <div class="stat-item">
            <div class="stat-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.total || 0 }}</div>
              <div class="stat-label">总订单数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-item">
            <div class="stat-icon success">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.success || 0 }}</div>
              <div class="stat-label">成功订单</div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-item">
            <div class="stat-icon amount">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">¥{{ formatPrice(statistics.totalAmount || 0) }}</div>
              <div class="stat-label">总金额</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 时间线主体 -->
    <div class="timeline-container" v-loading="loading">
      <div v-for="(group, date) in groupedOrders" :key="date" class="timeline-group">
        <!-- 日期分组头部 -->
        <div class="date-header">
          <div class="date-info">
            <div class="date-main">{{ formatDateHeader(date) }}</div>
            <div class="date-stats">
              <span class="order-count">{{ group.length }} 个订单</span>
              <span class="total-amount">总计: ¥{{ calculateGroupTotal(group) }}</span>
            </div>
          </div>
          <div class="date-actions">
            <el-button size="small" @click="exportGroupData(date, group)">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>

        <!-- 订单时间线 -->
        <el-timeline class="order-timeline">
          <el-timeline-item
            v-for="order in group"
            :key="order.order_no"
            :timestamp="formatTime(order.created_at)"
            :type="getTimelineType(order.status)"
            :icon="getTimelineIcon(order.status)"
            placement="top"
          >
            <div class="timeline-card" @click="viewOrderDetail(order)">
              <!-- 卡片头部 -->
              <div class="card-header">
                <div class="order-basic">
                  <div class="order-number">
                    <span class="label">订单号:</span>
                    <span class="value">{{ order.order_no }}</span>
                  </div>
                  <div v-if="order.tracking_no" class="tracking-number">
                    <span class="label">运单号:</span>
                    <span class="value">{{ order.tracking_no }}</span>
                  </div>
                </div>
                <div class="order-status">
                  <el-tag :type="getStatusType(order.status)" size="large">
                    {{ getStatusDesc(order.status) }}
                  </el-tag>
                </div>
              </div>

              <!-- 卡片内容 -->
              <div class="card-content">
                <el-row :gutter="16">
                  <!-- 左侧：基本信息 -->
                  <el-col :span="12">
                    <div class="info-section">
                      <div class="info-item">
                        <span class="label">快递公司:</span>
                        <span class="value">{{ getExpressName(order) }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">供应商:</span>
                        <el-tag :type="getProviderTagType(order.provider)" size="small">
                          {{ getProviderName(order) }}
                        </el-tag>
                      </div>
                      <div class="info-item">
                        <span class="label">重量:</span>
                        <span class="value">{{ formatWeight(order.weight) }}kg</span>
                      </div>
                      <div class="info-item">
                        <span class="label">费用:</span>
                        <span class="value price">¥{{ formatPrice(order.actual_fee || order.price || 0) }}</span>
                      </div>
                    </div>
                  </el-col>

                  <!-- 右侧：地址信息 -->
                  <el-col :span="12">
                    <div class="address-section">
                      <div class="address-item sender">
                        <div class="address-header">
                          <el-icon><User /></el-icon>
                          <span>寄件人</span>
                        </div>
                        <div class="address-detail">
                          <div class="name-phone">
                            {{ getSenderInfo(order).name }} {{ getSenderInfo(order).mobile }}
                          </div>
                          <div class="location">{{ getSenderInfo(order).city }}</div>
                        </div>
                      </div>
                      <div class="address-item receiver">
                        <div class="address-header">
                          <el-icon><UserFilled /></el-icon>
                          <span>收件人</span>
                        </div>
                        <div class="address-detail">
                          <div class="name-phone">
                            {{ getReceiverInfo(order).name }} {{ getReceiverInfo(order).mobile }}
                          </div>
                          <div class="location">{{ getReceiverInfo(order).city }}</div>
                        </div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- 卡片底部 -->
              <div class="card-footer">
                <div class="timeline-progress">
                  <div class="progress-steps">
                    <div
                      v-for="step in orderSteps"
                      :key="step.key"
                      :class="['progress-step', { active: isStepActive(order, step.key) }]"
                    >
                      <div class="step-icon">
                        <el-icon>
                          <component :is="step.icon" />
                        </el-icon>
                      </div>
                      <div class="step-label">{{ step.label }}</div>
                    </div>
                  </div>
                </div>
                <div class="card-actions">
                  <el-button size="small" @click.stop="viewOrderDetail(order)">
                    <el-icon><View /></el-icon>
                    详情
                  </el-button>
                  <el-button
                    v-if="canCancelOrder(order)"
                    size="small"
                    type="danger"
                    @click.stop="cancelOrder(order)"
                  >
                    <el-icon><Close /></el-icon>
                    取消
                  </el-button>
                  <el-button
                    v-if="canRetryOrder(order)"
                    size="small"
                    type="primary"
                    @click.stop="retryOrder(order)"
                  >
                    <el-icon><RefreshLeft /></el-icon>
                    重试
                  </el-button>
                </div>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && Object.keys(groupedOrders).length === 0" class="empty-state">
        <div class="empty-icon">
          <el-icon size="64" color="#c0c4cc">
            <Clock />
          </el-icon>
        </div>
        <div class="empty-text">
          <h3>暂无订单数据</h3>
          <p>当前时间范围内没有找到订单</p>
        </div>
        <div class="empty-actions">
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            创建订单
          </el-button>
        </div>
      </div>
    </div>

    <!-- 加载更多 -->
    <div v-if="hasMore" class="load-more">
      <el-button @click="loadMore" :loading="loadingMore">
        <el-icon><ArrowDown /></el-icon>
        加载更多
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus,
  Refresh,
  Search,
  Document,
  CircleCheck,
  Money,
  Download,
  User,
  UserFilled,
  View,
  Close,
  RefreshLeft,
  Clock,
  ArrowDown,
  Edit,
  Truck,
  Box,
  Flag
} from '@element-plus/icons-vue'
import { mockOrderList, mockStatistics, type MockOrderItem } from '@/mock/orderListMockData'

// 响应式数据
const loading = ref(false)
const loadingMore = ref(false)
const showCreateDialog = ref(false)
const timeRange = ref('today')
const customDateRange = ref<[string, string] | null>(null)
const searchKeyword = ref('')
const selectedStatuses = ref([])
const orderList = ref<MockOrderItem[]>(mockOrderList)
const hasMore = ref(true)

// 统计数据
const statistics = ref(mockStatistics)

// 订单状态选项
const orderStatusOptions = ref([
  { label: '待处理', value: 'pending' },
  { label: '已接单', value: 'accepted' },
  { label: '已揽件', value: 'picked_up' },
  { label: '运输中', value: 'in_transit' },
  { label: '已签收', value: 'delivered' },
  { label: '已取消', value: 'cancelled' },
  { label: '失败', value: 'failed' }
])

// 订单步骤
const orderSteps = ref([
  { key: 'created', label: '已创建', icon: 'Edit' },
  { key: 'accepted', label: '已接单', icon: 'CircleCheck' },
  { key: 'picked_up', label: '已揽件', icon: 'Box' },
  { key: 'in_transit', label: '运输中', icon: 'Truck' },
  { key: 'delivered', label: '已签收', icon: 'Flag' }
])

// 计算属性：按日期分组的订单
const groupedOrders = computed(() => {
  const groups: Record<string, any[]> = {}
  orderList.value.forEach((order: any) => {
    const date = new Date(order.created_at).toDateString()
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(order)
  })
  
  // 按日期排序
  const sortedGroups: Record<string, any[]> = {}
  Object.keys(groups)
    .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
    .forEach(date => {
      sortedGroups[date] = groups[date].sort((a, b) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )
    })
  
  return sortedGroups
})

// 方法
const handleTimeRangeChange = (value: string) => {
  console.log('时间范围变化', value)
}

const handleCustomDateChange = (value: [string, string] | null) => {
  console.log('自定义时间变化', value)
}

const handleSearch = () => {
  console.log('执行搜索')
}

const handleStatusFilter = (statuses: string[]) => {
  console.log('状态筛选', statuses)
}

const refreshTimeline = () => {
  console.log('刷新时间线')
}

const viewOrderDetail = (order: any) => {
  console.log('查看订单详情', order)
}

const cancelOrder = (order: any) => {
  console.log('取消订单', order)
}

const retryOrder = (order: any) => {
  console.log('重试订单', order)
}

const exportGroupData = (date: string, orders: any[]) => {
  console.log('导出分组数据', date, orders)
}

const loadMore = () => {
  loadingMore.value = true
  // 模拟加载更多
  setTimeout(() => {
    loadingMore.value = false
  }, 1000)
}

// 辅助方法
const formatDateHeader = (dateStr: string) => {
  const date = new Date(dateStr)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  
  if (date.toDateString() === today.toDateString()) {
    return '今天'
  } else if (date.toDateString() === yesterday.toDateString()) {
    return '昨天'
  } else {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })
  }
}

const formatTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const calculateGroupTotal = (orders: any[]) => {
  const total = orders.reduce((sum, order) => sum + (order.actual_fee || order.price || 0), 0)
  return formatPrice(total)
}

const getTimelineType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    accepted: 'primary',
    picked_up: 'info',
    in_transit: 'primary',
    delivered: 'success',
    cancelled: 'info',
    failed: 'danger'
  }
  return typeMap[status] || 'info'
}

const getTimelineIcon = (status: string) => {
  const iconMap: Record<string, any> = {
    pending: Clock,
    accepted: CircleCheck,
    picked_up: Box,
    in_transit: Truck,
    delivered: Flag,
    cancelled: Close,
    failed: Close
  }
  return iconMap[status] || Clock
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    accepted: 'primary',
    picked_up: 'info',
    in_transit: 'primary',
    delivered: 'success',
    cancelled: 'info',
    failed: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusDesc = (status: string) => {
  const descMap: Record<string, string> = {
    pending: '待处理',
    accepted: '已接单',
    picked_up: '已揽件',
    in_transit: '运输中',
    delivered: '已签收',
    cancelled: '已取消',
    failed: '失败'
  }
  return descMap[status] || status
}

const getSenderInfo = (order: any) => {
  try {
    return JSON.parse(order.sender_info || '{}')
  } catch {
    return { name: '-', mobile: '-', city: '-' }
  }
}

const getReceiverInfo = (order: any) => {
  try {
    return JSON.parse(order.receiver_info || '{}')
  } catch {
    return { name: '-', mobile: '-', city: '-' }
  }
}

const getExpressName = (order: any) => {
  return order.express_name || order.express_type || '-'
}

const getProviderName = (order: any) => {
  const nameMap: Record<string, string> = {
    cainiao: '菜鸟',
    jd: '京东',
    dbl: '德邦'
  }
  return nameMap[order.provider] || order.provider
}

const getProviderTagType = (provider: string) => {
  const typeMap: Record<string, string> = {
    cainiao: 'warning',
    jd: 'danger',
    dbl: 'success'
  }
  return typeMap[provider] || 'info'
}

const formatWeight = (weight: number) => {
  return weight ? weight.toFixed(2) : '0.00'
}

const formatPrice = (price: number) => {
  return price ? price.toFixed(2) : '0.00'
}

const canCancelOrder = (order: any) => {
  return ['pending', 'accepted'].includes(order.status)
}

const canRetryOrder = (order: any) => {
  return order.status === 'failed'
}

const isStepActive = (order: any, stepKey: string) => {
  const statusOrder = ['created', 'accepted', 'picked_up', 'in_transit', 'delivered']
  const currentIndex = statusOrder.indexOf(order.status)
  const stepIndex = statusOrder.indexOf(stepKey)
  return stepIndex <= currentIndex
}

onMounted(() => {
  // 初始化数据已通过模拟数据加载
  console.log('订单列表设计3已加载', orderList.value.length, '个订单')
})
</script>

<style scoped lang="scss">
/* 时间线式布局样式 */
.timeline-order-list {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;

  /* 控制面板样式 */
  .control-panel {
    margin-bottom: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h2 {
        margin: 0;
        color: #303133;
        font-weight: 600;
      }

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }

    .time-selector {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 16px;

      .el-radio-group {
        .el-radio-button {
          :deep(.el-radio-button__inner) {
            border-radius: 6px;
            margin-right: 4px;
            border: 1px solid #dcdfe6;
            font-weight: 500;
          }

          &.is-active {
            :deep(.el-radio-button__inner) {
              background: #409eff;
              border-color: #409eff;
              color: white;
            }
          }
        }
      }
    }

    .quick-search {
      margin-bottom: 16px;

      .el-input {
        max-width: 400px;

        :deep(.el-input__wrapper) {
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
      }
    }

    .status-filters {
      .el-checkbox-group {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;

        .el-checkbox-button {
          :deep(.el-checkbox-button__inner) {
            border-radius: 20px;
            border: 1px solid #dcdfe6;
            padding: 8px 16px;
            background: white;

            .el-tag {
              border: none;
              background: transparent;
            }
          }

          &.is-checked {
            :deep(.el-checkbox-button__inner) {
              background: #f0f9ff;
              border-color: #409eff;
            }
          }
        }
      }
    }
  }

  /* 统计概览样式 */
  .stats-overview {
    margin-bottom: 20px;

    .stat-item {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      display: flex;
      align-items: center;
      gap: 16px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
      }

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;

        &.total {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.success {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.amount {
          background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
      }

      .stat-content {
        .stat-number {
          font-size: 24px;
          font-weight: 700;
          color: #303133;
          line-height: 1;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
          font-weight: 500;
        }
      }
    }
  }

  /* 时间线容器样式 */
  .timeline-container {
    .timeline-group {
      margin-bottom: 32px;

      .date-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        background: white;
        border-radius: 12px 12px 0 0;
        border-bottom: 2px solid #f0f2f5;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .date-info {
          .date-main {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }

          .date-stats {
            display: flex;
            gap: 16px;
            font-size: 14px;
            color: #606266;

            .order-count {
              color: #409eff;
              font-weight: 500;
            }

            .total-amount {
              color: #f56c6c;
              font-weight: 600;
            }
          }
        }

        .date-actions {
          .el-button {
            border-radius: 6px;
          }
        }
      }

      .order-timeline {
        background: white;
        padding: 20px;
        border-radius: 0 0 12px 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        :deep(.el-timeline-item) {
          .el-timeline-item__timestamp {
            font-weight: 600;
            color: #606266;
            font-size: 14px;
          }

          .el-timeline-item__node {
            width: 16px;
            height: 16px;
            border-width: 3px;
          }

          .el-timeline-item__content {
            padding-left: 24px;
          }
        }

        .timeline-card {
          background: #fafbfc;
          border: 1px solid #e4e7ed;
          border-radius: 8px;
          padding: 16px;
          cursor: pointer;
          transition: all 0.3s ease;
          margin-bottom: 16px;

          &:hover {
            background: white;
            border-color: #409eff;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
            transform: translateY(-1px);
          }

          .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .order-basic {
              .order-number {
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 4px;

                .label {
                  color: #909399;
                }

                .value {
                  color: #409eff;
                  font-family: 'SF Mono', 'Monaco', monospace;
                }
              }

              .tracking-number {
                font-size: 14px;

                .label {
                  color: #909399;
                }

                .value {
                  color: #67c23a;
                  font-family: 'SF Mono', 'Monaco', monospace;
                  font-weight: 600;
                }
              }
            }

            .order-status {
              .el-tag {
                font-weight: 600;
                border-radius: 20px;
                padding: 8px 16px;
              }
            }
          }

          .card-content {
            margin-bottom: 16px;

            .info-section {
              .info-item {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 8px;

                .label {
                  color: #909399;
                  font-size: 14px;
                  min-width: 60px;
                }

                .value {
                  color: #303133;
                  font-weight: 500;

                  &.price {
                    color: #f56c6c;
                    font-weight: 600;
                    font-size: 16px;
                  }
                }
              }
            }

            .address-section {
              .address-item {
                margin-bottom: 12px;
                padding: 12px;
                background: white;
                border-radius: 6px;
                border: 1px solid #f0f2f5;

                &.sender {
                  border-left: 3px solid #409eff;
                }

                &.receiver {
                  border-left: 3px solid #67c23a;
                }

                .address-header {
                  display: flex;
                  align-items: center;
                  gap: 6px;
                  margin-bottom: 6px;
                  font-weight: 600;
                  color: #606266;
                  font-size: 14px;
                }

                .address-detail {
                  .name-phone {
                    font-weight: 600;
                    color: #303133;
                    margin-bottom: 2px;
                  }

                  .location {
                    font-size: 13px;
                    color: #909399;
                  }
                }
              }
            }
          }

          .card-footer {
            .timeline-progress {
              margin-bottom: 12px;

              .progress-steps {
                display: flex;
                justify-content: space-between;
                align-items: center;
                position: relative;

                &::before {
                  content: '';
                  position: absolute;
                  top: 50%;
                  left: 0;
                  right: 0;
                  height: 2px;
                  background: #e4e7ed;
                  z-index: 1;
                }

                .progress-step {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  gap: 4px;
                  position: relative;
                  z-index: 2;

                  .step-icon {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    background: #f5f7fa;
                    border: 2px solid #e4e7ed;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #c0c4cc;
                    transition: all 0.3s ease;
                  }

                  .step-label {
                    font-size: 12px;
                    color: #c0c4cc;
                    font-weight: 500;
                    transition: all 0.3s ease;
                  }

                  &.active {
                    .step-icon {
                      background: #409eff;
                      border-color: #409eff;
                      color: white;
                    }

                    .step-label {
                      color: #409eff;
                      font-weight: 600;
                    }
                  }
                }
              }
            }

            .card-actions {
              display: flex;
              justify-content: flex-end;
              gap: 8px;

              .el-button {
                border-radius: 6px;
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }

  /* 空状态样式 */
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    text-align: center;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .empty-icon {
      margin-bottom: 20px;
    }

    .empty-text {
      h3 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 500;
        color: #303133;
      }

      p {
        margin: 0 0 20px 0;
        font-size: 14px;
        color: #606266;
      }
    }

    .empty-actions {
      .el-button {
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
      }
    }
  }

  /* 加载更多样式 */
  .load-more {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    .el-button {
      padding: 12px 32px;
      border-radius: 8px;
      font-weight: 500;
    }
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .stats-overview .el-col {
      margin-bottom: 16px;
    }
  }

  @media (max-width: 768px) {
    padding: 12px;

    .control-panel {
      .panel-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
      }

      .time-selector {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
      }

      .status-filters .el-checkbox-group {
        justify-content: center;
      }
    }

    .timeline-container .timeline-group {
      .date-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
      }

      .order-timeline .timeline-card {
        .card-content .el-row {
          .el-col {
            margin-bottom: 16px;
          }
        }

        .card-footer {
          .timeline-progress .progress-steps {
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;

            &::before {
              display: none;
            }
          }

          .card-actions {
            justify-content: center;
            flex-wrap: wrap;
          }
        }
      }
    }
  }
}
</style>
