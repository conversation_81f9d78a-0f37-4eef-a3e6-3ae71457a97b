<!-- 设计方案2：紧凑表格式布局 - 信息密度极高，适合专业用户 -->
<template>
  <div class="compact-order-list">
    <!-- 工具栏 -->
    <el-card class="toolbar-card">
      <div class="toolbar">
        <!-- 左侧操作 -->
        <div class="toolbar-left">
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            新建订单
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="exportData">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>

        <!-- 中间搜索 -->
        <div class="toolbar-center">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索订单号、运单号、收件人..."
            clearable
            @keyup.enter="handleSearch"
            style="width: 400px;"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 右侧筛选 -->
        <div class="toolbar-right">
          <el-select v-model="quickFilter" placeholder="快速筛选" @change="handleQuickFilter">
            <el-option label="全部订单" value="" />
            <el-option label="今日订单" value="today" />
            <el-option label="待处理" value="pending" />
            <el-option label="已完成" value="completed" />
            <el-option label="异常订单" value="exception" />
          </el-select>
          <el-button @click="showAdvancedFilter = !showAdvancedFilter">
            <el-icon><Filter /></el-icon>
            高级筛选
          </el-button>
        </div>
      </div>

      <!-- 高级筛选面板 -->
      <el-collapse-transition>
        <div v-show="showAdvancedFilter" class="advanced-filter-panel">
          <el-form :model="filterForm" inline>
            <el-form-item label="订单状态">
              <el-select v-model="filterForm.status" placeholder="选择状态" clearable multiple>
                <el-option
                  v-for="status in orderStatusOptions"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="快递公司">
              <el-select v-model="filterForm.express_type" placeholder="选择快递公司" clearable multiple>
                <el-option
                  v-for="company in expressCompanyOptions"
                  :key="company.value"
                  :label="company.label"
                  :value="company.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="供应商">
              <el-select v-model="filterForm.provider" placeholder="选择供应商" clearable multiple>
                <el-option label="菜鸟" value="cainiao" />
                <el-option label="京东" value="jd" />
                <el-option label="德邦" value="dbl" />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="filterForm.dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="金额范围">
              <el-input-number v-model="filterForm.minAmount" placeholder="最小金额" :min="0" />
              <span style="margin: 0 8px;">-</span>
              <el-input-number v-model="filterForm.maxAmount" placeholder="最大金额" :min="0" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="applyFilter">应用筛选</el-button>
              <el-button @click="resetFilter">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-collapse-transition>
    </el-card>

    <!-- 数据统计栏 -->
    <div class="stats-bar">
      <div class="stats-item">
        <span class="stats-label">总订单：</span>
        <span class="stats-value">{{ statistics.total || 0 }}</span>
      </div>
      <div class="stats-item">
        <span class="stats-label">今日新增：</span>
        <span class="stats-value highlight">{{ statistics.today || 0 }}</span>
      </div>
      <div class="stats-item">
        <span class="stats-label">待处理：</span>
        <span class="stats-value warning">{{ statistics.pending || 0 }}</span>
      </div>
      <div class="stats-item">
        <span class="stats-label">已完成：</span>
        <span class="stats-value success">{{ statistics.completed || 0 }}</span>
      </div>
      <div class="stats-item">
        <span class="stats-label">异常订单：</span>
        <span class="stats-value danger">{{ statistics.exception || 0 }}</span>
      </div>
      <div class="stats-item">
        <span class="stats-label">总金额：</span>
        <span class="stats-value amount">¥{{ formatPrice(statistics.totalAmount || 0) }}</span>
      </div>
    </div>

    <!-- 订单表格 -->
    <el-card class="table-card">
      <el-table
        :data="orderList"
        v-loading="loading"
        stripe
        border
        size="small"
        :row-class-name="getRowClassName"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        style="width: 100%"
      >
        <!-- 选择列 -->
        <el-table-column type="selection" width="50" fixed="left" />
        
        <!-- 序号列 -->
        <el-table-column type="index" label="#" width="60" fixed="left" />

        <!-- 订单信息 -->
        <el-table-column prop="order_no" label="平台订单号" width="140" fixed="left" sortable="custom">
          <template #default="{ row }">
            <div class="order-number">
              <el-link type="primary" @click="viewOrderDetail(row)">
                {{ row.order_no }}
              </el-link>
              <el-tag v-if="row.customer_order_no" size="small" type="info" class="customer-order-tag">
                客户: {{ row.customer_order_no }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="tracking_no" label="运单号" width="140" sortable="custom">
          <template #default="{ row }">
            <div v-if="row.tracking_no" class="tracking-number">
              <span class="tracking-text">{{ row.tracking_no }}</span>
              <el-button size="small" text @click="trackOrder(row)">
                <el-icon><View /></el-icon>
              </el-button>
            </div>
            <span v-else class="no-tracking">-</span>
          </template>
        </el-table-column>

        <!-- 状态信息 -->
        <el-table-column prop="status" label="状态" width="100" sortable="custom">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusDesc(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 快递信息 -->
        <el-table-column label="快递信息" width="160">
          <template #default="{ row }">
            <div class="express-info">
              <div class="express-name">{{ getExpressName(row) }}</div>
              <el-tag :type="getProviderTagType(row.provider)" size="small">
                {{ getProviderName(row) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <!-- 地址信息 -->
        <el-table-column label="寄收件信息" width="300" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="address-info">
              <div class="address-row sender">
                <el-icon><User /></el-icon>
                <span class="name">{{ getSenderInfo(row).name }}</span>
                <span class="phone">{{ getSenderInfo(row).mobile }}</span>
                <span class="location">{{ getSenderInfo(row).city }}</span>
              </div>
              <div class="address-row receiver">
                <el-icon><UserFilled /></el-icon>
                <span class="name">{{ getReceiverInfo(row).name }}</span>
                <span class="phone">{{ getReceiverInfo(row).mobile }}</span>
                <span class="location">{{ getReceiverInfo(row).city }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 重量体积 -->
        <el-table-column label="重量/体积" width="120" sortable="custom">
          <template #default="{ row }">
            <div class="weight-volume">
              <div class="weight">{{ formatWeight(row.weight) }}kg</div>
              <div class="volume">{{ formatVolume(row.order_volume) }}</div>
              <el-tag
                v-if="getWeightAnomalyType(row)"
                :type="getWeightAnomalyTagType(row)"
                size="small"
                class="anomaly-tag"
              >
                {{ getWeightAnomalyText(row) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <!-- 费用信息 -->
        <el-table-column label="费用" width="120" sortable="custom">
          <template #default="{ row }">
            <div class="fee-info">
              <div class="estimated">预: ¥{{ formatPrice(row.price || 0) }}</div>
              <div class="actual">实: ¥{{ formatPrice(row.actual_fee || 0) }}</div>
              <div v-if="row.actual_fee !== row.price" class="difference">
                <span :class="row.actual_fee > row.price ? 'increase' : 'decrease'">
                  {{ row.actual_fee > row.price ? '+' : '' }}¥{{ formatPrice((row.actual_fee || 0) - (row.price || 0)) }}
                </span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 时间信息 -->
        <el-table-column label="时间信息" width="160" sortable="custom">
          <template #default="{ row }">
            <div class="time-info">
              <div class="created-time">
                <span class="label">创建:</span>
                <span class="time">{{ formatDateTime(row.created_at) }}</span>
              </div>
              <div v-if="hasPickupTime(row)" class="pickup-time">
                <span class="label">预约:</span>
                <span class="time">{{ formatPickupTime(row) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button size="small" type="primary" @click="viewOrderDetail(row)">
                详情
              </el-button>
              <el-dropdown @command="handleCommand" trigger="click">
                <el-button size="small">
                  更多<el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-if="canCancelOrder(row)"
                      :command="{ action: 'cancel', row }"
                      icon="Close"
                    >
                      取消订单
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="canRetryOrder(row)"
                      :command="{ action: 'retry', row }"
                      icon="RefreshLeft"
                    >
                      重试下单
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{ action: 'track', row }"
                      icon="View"
                    >
                      物流跟踪
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{ action: 'afterSales', row }"
                      icon="Service"
                    >
                      申请售后
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{ action: 'copy', row }"
                      icon="CopyDocument"
                    >
                      复制信息
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作栏 -->
      <div v-if="selectedOrders.length > 0" class="batch-actions">
        <div class="batch-info">
          已选择 <strong>{{ selectedOrders.length }}</strong> 个订单
        </div>
        <div class="batch-buttons">
          <el-button size="small" @click="batchCancel">批量取消</el-button>
          <el-button size="small" @click="batchExport">批量导出</el-button>
          <el-button size="small" @click="batchTrack">批量跟踪</el-button>
          <el-button size="small" type="danger" @click="clearSelection">清除选择</el-button>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[20, 50, 100, 200]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus,
  Refresh,
  Download,
  Search,
  Filter,
  View,
  User,
  UserFilled,
  ArrowDown,
  Close,
  RefreshLeft,
  Service,
  CopyDocument
} from '@element-plus/icons-vue'
import { mockOrderList, mockStatistics, type MockOrderItem } from '@/mock/orderListMockData'

// 响应式数据
const loading = ref(false)
const showCreateDialog = ref(false)
const showAdvancedFilter = ref(false)
const searchKeyword = ref('')
const quickFilter = ref('')
const orderList = ref<MockOrderItem[]>(mockOrderList)
const selectedOrders = ref([])

// 统计数据
const statistics = ref(mockStatistics)

// 筛选表单
const filterForm = reactive({
  status: [],
  express_type: [],
  provider: [],
  dateRange: null,
  minAmount: undefined,
  maxAmount: undefined
})

// 分页信息
const pagination = reactive({
  page: 1,
  page_size: 50,
  total: mockOrderList.length
})

// 订单状态选项
const orderStatusOptions = ref([
  { label: '待处理', value: 'pending' },
  { label: '已接单', value: 'accepted' },
  { label: '已揽件', value: 'picked_up' },
  { label: '运输中', value: 'in_transit' },
  { label: '已签收', value: 'delivered' },
  { label: '已取消', value: 'cancelled' },
  { label: '失败', value: 'failed' }
])

// 快递公司选项
const expressCompanyOptions = ref([
  { label: '顺丰速运', value: 'SF' },
  { label: '圆通速递', value: 'YTO' },
  { label: '中通快递', value: 'ZTO' },
  { label: '申通快递', value: 'STO' },
  { label: '韵达速递', value: 'YD' },
  { label: '京东快递', value: 'JD' },
  { label: '德邦快递', value: 'DBL' }
])

// 方法
const handleSearch = () => {
  console.log('执行搜索')
}

const handleQuickFilter = (value: string) => {
  console.log('快速筛选', value)
}

const applyFilter = () => {
  console.log('应用筛选')
}

const resetFilter = () => {
  Object.keys(filterForm).forEach(key => {
    filterForm[key] = Array.isArray(filterForm[key]) ? [] : undefined
  })
}

const refreshData = () => {
  console.log('刷新数据')
}

const exportData = () => {
  console.log('导出数据')
}

const viewOrderDetail = (order: any) => {
  console.log('查看订单详情', order)
}

const trackOrder = (order: any) => {
  console.log('跟踪订单', order)
}

const handleCommand = (command: any) => {
  const { action, row } = command
  console.log('执行操作', action, row)
}

const handleSelectionChange = (selection: any[]) => {
  selectedOrders.value = selection
}

const handleSortChange = (sort: any) => {
  console.log('排序变化', sort)
}

const clearSelection = () => {
  selectedOrders.value = []
}

const batchCancel = () => {
  console.log('批量取消')
}

const batchExport = () => {
  console.log('批量导出')
}

const batchTrack = () => {
  console.log('批量跟踪')
}

const handleSizeChange = (size: number) => {
  pagination.page_size = size
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
}

// 辅助方法
const getRowClassName = ({ row, rowIndex }: any) => {
  if (row.status === 'failed') return 'failed-row'
  if (row.status === 'exception') return 'exception-row'
  return ''
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    accepted: 'primary',
    picked_up: 'info',
    in_transit: 'primary',
    delivered: 'success',
    cancelled: 'info',
    failed: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusDesc = (status: string) => {
  const descMap: Record<string, string> = {
    pending: '待处理',
    accepted: '已接单',
    picked_up: '已揽件',
    in_transit: '运输中',
    delivered: '已签收',
    cancelled: '已取消',
    failed: '失败'
  }
  return descMap[status] || status
}

const getSenderInfo = (order: any) => {
  try {
    return JSON.parse(order.sender_info || '{}')
  } catch {
    return { name: '-', mobile: '-', city: '-' }
  }
}

const getReceiverInfo = (order: any) => {
  try {
    return JSON.parse(order.receiver_info || '{}')
  } catch {
    return { name: '-', mobile: '-', city: '-' }
  }
}

const getExpressName = (order: any) => {
  return order.express_name || order.express_type || '-'
}

const getProviderName = (order: any) => {
  const nameMap: Record<string, string> = {
    cainiao: '菜鸟',
    jd: '京东',
    dbl: '德邦'
  }
  return nameMap[order.provider] || order.provider
}

const getProviderTagType = (provider: string) => {
  const typeMap: Record<string, string> = {
    cainiao: 'warning',
    jd: 'danger',
    dbl: 'success'
  }
  return typeMap[provider] || 'info'
}

const formatWeight = (weight: number) => {
  return weight ? weight.toFixed(2) : '0.00'
}

const formatVolume = (volume: number) => {
  return volume ? `${volume.toFixed(4)}m³` : '-'
}

const formatPrice = (price: number) => {
  return price ? price.toFixed(2) : '0.00'
}

const formatDateTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const hasPickupTime = (order: any) => {
  return order.pickup_start_time && order.pickup_end_time
}

const formatPickupTime = (order: any) => {
  if (!hasPickupTime(order)) return '-'
  const start = new Date(order.pickup_start_time).toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
  return start
}

const canCancelOrder = (order: any) => {
  return ['pending', 'accepted'].includes(order.status)
}

const canRetryOrder = (order: any) => {
  return order.status === 'failed'
}

const getWeightAnomalyType = (order: any) => {
  // 实现重量异常检测逻辑
  return null
}

const getWeightAnomalyTagType = (order: any) => {
  return 'warning'
}

const getWeightAnomalyText = (order: any) => {
  return '异常'
}

onMounted(() => {
  // 初始化数据已通过模拟数据加载
  console.log('订单列表设计2已加载', orderList.value.length, '个订单')
})
</script>

<style scoped lang="scss">
/* 紧凑表格式布局样式 */
.compact-order-list {
  padding: 16px;
  background: #f5f7fa;
  min-height: 100vh;

  /* 工具栏样式 */
  .toolbar-card {
    margin-bottom: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;

      .toolbar-left {
        display: flex;
        gap: 8px;
      }

      .toolbar-center {
        flex: 1;
        display: flex;
        justify-content: center;
      }

      .toolbar-right {
        display: flex;
        gap: 8px;
      }
    }

    .advanced-filter-panel {
      margin-top: 16px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e4e7ed;

      .el-form-item {
        margin-bottom: 16px;
      }
    }
  }

  /* 统计栏样式 */
  .stats-bar {
    display: flex;
    gap: 24px;
    padding: 12px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin-bottom: 16px;
    align-items: center;

    .stats-item {
      display: flex;
      align-items: center;
      gap: 4px;

      .stats-label {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
      }

      .stats-value {
        font-size: 16px;
        font-weight: 700;
        color: #303133;

        &.highlight {
          color: #409eff;
        }

        &.warning {
          color: #e6a23c;
        }

        &.success {
          color: #67c23a;
        }

        &.danger {
          color: #f56c6c;
        }

        &.amount {
          color: #f56c6c;
          font-size: 18px;
        }
      }
    }
  }

  /* 表格卡片样式 */
  .table-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    :deep(.el-table) {
      font-size: 13px;

      .el-table__header {
        background: #f8f9fa;

        th {
          background: #f8f9fa !important;
          color: #606266;
          font-weight: 600;
          border-bottom: 2px solid #e4e7ed;
        }
      }

      .el-table__row {
        &.failed-row {
          background: #fef0f0;
        }

        &.exception-row {
          background: #fdf6ec;
        }

        &:hover {
          background: #f5f7fa !important;
        }
      }

      .el-table__cell {
        padding: 8px 0;
        border-bottom: 1px solid #f0f2f5;
      }
    }

    /* 订单号列样式 */
    .order-number {
      .el-link {
        font-weight: 600;
        font-family: 'SF Mono', 'Monaco', monospace;
      }

      .customer-order-tag {
        margin-top: 2px;
        font-size: 11px;
      }
    }

    /* 运单号列样式 */
    .tracking-number {
      display: flex;
      align-items: center;
      gap: 4px;

      .tracking-text {
        font-family: 'SF Mono', 'Monaco', monospace;
        font-weight: 600;
        color: #67c23a;
      }
    }

    .no-tracking {
      color: #c0c4cc;
    }

    /* 快递信息样式 */
    .express-info {
      .express-name {
        font-weight: 600;
        color: #303133;
        margin-bottom: 2px;
      }
    }

    /* 地址信息样式 */
    .address-info {
      .address-row {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 4px;
        font-size: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        &.sender {
          color: #409eff;
        }

        &.receiver {
          color: #67c23a;
        }

        .name {
          font-weight: 600;
          min-width: 50px;
        }

        .phone {
          color: #909399;
          font-family: 'SF Mono', 'Monaco', monospace;
        }

        .location {
          color: #606266;
        }
      }
    }

    /* 重量体积样式 */
    .weight-volume {
      .weight {
        font-weight: 600;
        color: #303133;
      }

      .volume {
        font-size: 12px;
        color: #909399;
      }

      .anomaly-tag {
        margin-top: 2px;
        font-size: 11px;
      }
    }

    /* 费用信息样式 */
    .fee-info {
      .estimated {
        color: #909399;
        font-size: 12px;
      }

      .actual {
        color: #303133;
        font-weight: 600;
      }

      .difference {
        font-size: 11px;
        margin-top: 2px;

        .increase {
          color: #f56c6c;
        }

        .decrease {
          color: #67c23a;
        }
      }
    }

    /* 时间信息样式 */
    .time-info {
      font-size: 12px;

      .created-time,
      .pickup-time {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 2px;

        .label {
          color: #909399;
          min-width: 30px;
        }

        .time {
          color: #606266;
          font-family: 'SF Mono', 'Monaco', monospace;
        }
      }
    }

    /* 操作按钮样式 */
    .action-buttons {
      display: flex;
      gap: 4px;

      .el-button {
        padding: 4px 8px;
        font-size: 12px;
      }
    }

    /* 批量操作栏样式 */
    .batch-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #f0f9ff;
      border: 1px solid #bfdbfe;
      border-radius: 6px;
      margin: 16px 0;

      .batch-info {
        color: #1e40af;
        font-weight: 600;
      }

      .batch-buttons {
        display: flex;
        gap: 8px;

        .el-button {
          padding: 6px 12px;
          font-size: 12px;
        }
      }
    }

    /* 分页样式 */
    .pagination-wrapper {
      display: flex;
      justify-content: center;
      padding: 20px 0;
      border-top: 1px solid #f0f2f5;
      margin-top: 16px;
    }
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .toolbar {
      flex-direction: column;
      gap: 12px;

      .toolbar-left,
      .toolbar-center,
      .toolbar-right {
        width: 100%;
        justify-content: center;
      }
    }

    .stats-bar {
      flex-wrap: wrap;
      gap: 12px;
    }
  }

  @media (max-width: 768px) {
    padding: 8px;

    .stats-bar {
      flex-direction: column;
      align-items: stretch;

      .stats-item {
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #f0f2f5;

        &:last-child {
          border-bottom: none;
        }
      }
    }

    :deep(.el-table) {
      font-size: 12px;

      .el-table__cell {
        padding: 6px 0;
      }
    }

    .batch-actions {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .batch-buttons {
        justify-content: center;
      }
    }
  }
}
</style>
