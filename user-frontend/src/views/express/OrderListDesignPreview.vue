<!-- 订单列表设计方案预览页面 -->
<template>
  <div class="design-preview">
    <div class="preview-header">
      <h1>订单列表设计方案预览</h1>
      <p>选择最适合您需求的订单列表展示方案</p>
    </div>

    <div class="design-cards">
      <el-row :gutter="20">
        <!-- 方案1：现代卡片式布局 -->
        <el-col :span="12">
          <div class="design-card" @click="selectDesign(1)">
            <div class="design-header">
              <h3>方案1：现代卡片式布局</h3>
              <el-tag type="primary">推荐</el-tag>
            </div>
            <div class="design-preview-image design1-preview">
              <div class="preview-placeholder">
                <el-icon size="48"><Grid /></el-icon>
                <span>现代卡片式布局</span>
              </div>
            </div>
            <div class="design-description">
              <h4>特点：</h4>
              <ul>
                <li>✅ 信息密度高，视觉层次清晰</li>
                <li>✅ 统计卡片直观展示关键指标</li>
                <li>✅ 智能搜索栏支持多种搜索方式</li>
                <li>✅ 卡片式订单展示，信息完整</li>
                <li>✅ 地址信息清晰对比展示</li>
                <li>✅ 响应式设计，移动端友好</li>
              </ul>
              <h4>适用场景：</h4>
              <p>适合需要快速浏览订单详情的业务人员，信息展示完整，操作便捷。</p>
            </div>
            <div class="design-actions">
              <el-button type="primary" @click.stop="previewDesign(1)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button @click.stop="selectDesign(1)">
                <el-icon><Select /></el-icon>
                选择此方案
              </el-button>
            </div>
          </div>
        </el-col>

        <!-- 方案2：紧凑表格式布局 -->
        <el-col :span="12">
          <div class="design-card" @click="selectDesign(2)">
            <div class="design-header">
              <h3>方案2：紧凑表格式布局</h3>
              <el-tag type="success">专业</el-tag>
            </div>
            <div class="design-preview-image design2-preview">
              <div class="preview-placeholder">
                <el-icon size="48"><List /></el-icon>
                <span>紧凑表格式布局</span>
              </div>
            </div>
            <div class="design-description">
              <h4>特点：</h4>
              <ul>
                <li>✅ 信息密度极高，适合专业用户</li>
                <li>✅ 强大的筛选和排序功能</li>
                <li>✅ 批量操作支持，提高效率</li>
                <li>✅ 表格形式展示，数据对比清晰</li>
                <li>✅ 支持多选和批量处理</li>
                <li>✅ 详细的统计信息栏</li>
              </ul>
              <h4>适用场景：</h4>
              <p>适合需要处理大量订单的专业用户，支持批量操作和数据分析。</p>
            </div>
            <div class="design-actions">
              <el-button type="primary" @click.stop="previewDesign(2)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button @click.stop="selectDesign(2)">
                <el-icon><Select /></el-icon>
                选择此方案
              </el-button>
            </div>
          </div>
        </el-col>

        <!-- 方案3：时间线式布局 -->
        <el-col :span="12">
          <div class="design-card" @click="selectDesign(3)">
            <div class="design-header">
              <h3>方案3：时间线式布局</h3>
              <el-tag type="warning">创新</el-tag>
            </div>
            <div class="design-preview-image design3-preview">
              <div class="preview-placeholder">
                <el-icon size="48"><Clock /></el-icon>
                <span>时间线式布局</span>
              </div>
            </div>
            <div class="design-description">
              <h4>特点：</h4>
              <ul>
                <li>✅ 以时间为主线，直观展示订单流程</li>
                <li>✅ 按日期分组，便于时间管理</li>
                <li>✅ 订单进度可视化展示</li>
                <li>✅ 时间线形式，流程清晰</li>
                <li>✅ 支持时间范围筛选</li>
                <li>✅ 适合追踪订单历史</li>
              </ul>
              <h4>适用场景：</h4>
              <p>适合需要按时间追踪订单进展的管理人员，时间线展示直观清晰。</p>
            </div>
            <div class="design-actions">
              <el-button type="primary" @click.stop="previewDesign(3)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button @click.stop="selectDesign(3)">
                <el-icon><Select /></el-icon>
                选择此方案
              </el-button>
            </div>
          </div>
        </el-col>

        <!-- 方案4：仪表板式布局 -->
        <el-col :span="12">
          <div class="design-card" @click="selectDesign(4)">
            <div class="design-header">
              <h3>方案4：仪表板式布局</h3>
              <el-tag type="danger">高级</el-tag>
            </div>
            <div class="design-preview-image design4-preview">
              <div class="preview-placeholder">
                <el-icon size="48"><DataBoard /></el-icon>
                <span>仪表板式布局</span>
              </div>
            </div>
            <div class="design-description">
              <h4>特点：</h4>
              <ul>
                <li>✅ 数据可视化，适合管理层查看</li>
                <li>✅ 关键指标突出展示</li>
                <li>✅ 图表分析功能丰富</li>
                <li>✅ 网格和列表双视图切换</li>
                <li>✅ 智能筛选和分析</li>
                <li>✅ 趋势分析和报表导出</li>
              </ul>
              <h4>适用场景：</h4>
              <p>适合管理层和数据分析人员，提供丰富的数据可视化和分析功能。</p>
            </div>
            <div class="design-actions">
              <el-button type="primary" @click.stop="previewDesign(4)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button @click.stop="selectDesign(4)">
                <el-icon><Select /></el-icon>
                选择此方案
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 对比表格 -->
    <el-card class="comparison-table">
      <template #header>
        <h3>方案对比</h3>
      </template>
      <el-table :data="comparisonData" border>
        <el-table-column prop="feature" label="功能特性" width="200" />
        <el-table-column label="方案1 - 现代卡片式" align="center">
          <template #default="{ row }">
            <el-icon v-if="row.design1" color="#67c23a"><Check /></el-icon>
            <el-icon v-else color="#f56c6c"><Close /></el-icon>
          </template>
        </el-table-column>
        <el-table-column label="方案2 - 紧凑表格式" align="center">
          <template #default="{ row }">
            <el-icon v-if="row.design2" color="#67c23a"><Check /></el-icon>
            <el-icon v-else color="#f56c6c"><Close /></el-icon>
          </template>
        </el-table-column>
        <el-table-column label="方案3 - 时间线式" align="center">
          <template #default="{ row }">
            <el-icon v-if="row.design3" color="#67c23a"><Check /></el-icon>
            <el-icon v-else color="#f56c6c"><Close /></el-icon>
          </template>
        </el-table-column>
        <el-table-column label="方案4 - 仪表板式" align="center">
          <template #default="{ row }">
            <el-icon v-if="row.design4" color="#67c23a"><Check /></el-icon>
            <el-icon v-else color="#f56c6c"><Close /></el-icon>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      :title="`预览 - ${currentDesignTitle}`"
      width="90%"
      :before-close="closePreview"
    >
      <div class="preview-content">
        <component :is="currentPreviewComponent" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { View, Select, Check, Close, Grid, List, Clock, DataBoard } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { mockStatistics } from '@/mock/orderListMockData'

// 导入设计方案组件
import OrderListDesign1 from './OrderListDesign1.vue'
import OrderListDesign2 from './OrderListDesign2.vue'
import OrderListDesign3 from './OrderListDesign3.vue'
import OrderListDesign4 from './OrderListDesign4.vue'

const router = useRouter()

// 响应式数据
const showPreviewDialog = ref(false)
const currentDesign = ref(1)

// 设计方案信息
const designInfo = {
  1: { title: '现代卡片式布局', component: OrderListDesign1 },
  2: { title: '紧凑表格式布局', component: OrderListDesign2 },
  3: { title: '时间线式布局', component: OrderListDesign3 },
  4: { title: '仪表板式布局', component: OrderListDesign4 }
}

// 计算属性
const currentDesignTitle = computed(() => {
  return designInfo[currentDesign.value]?.title || ''
})

const currentPreviewComponent = computed(() => {
  return designInfo[currentDesign.value]?.component
})

// 对比数据
const comparisonData = ref([
  {
    feature: '信息密度',
    design1: true,
    design2: true,
    design3: true,
    design4: true
  },
  {
    feature: '视觉美观',
    design1: true,
    design2: false,
    design3: true,
    design4: true
  },
  {
    feature: '批量操作',
    design1: false,
    design2: true,
    design3: false,
    design4: true
  },
  {
    feature: '数据可视化',
    design1: false,
    design2: false,
    design3: false,
    design4: true
  },
  {
    feature: '时间线展示',
    design1: false,
    design2: false,
    design3: true,
    design4: false
  },
  {
    feature: '移动端适配',
    design1: true,
    design2: true,
    design3: true,
    design4: true
  },
  {
    feature: '搜索筛选',
    design1: true,
    design2: true,
    design3: true,
    design4: true
  },
  {
    feature: '统计分析',
    design1: true,
    design2: true,
    design3: true,
    design4: true
  },
  {
    feature: '操作便捷性',
    design1: true,
    design2: true,
    design3: true,
    design4: false
  },
  {
    feature: '学习成本',
    design1: false, // 低学习成本
    design2: true,  // 高学习成本
    design3: false, // 低学习成本
    design4: true   // 高学习成本
  }
])

// 方法
const previewDesign = (designNumber: number) => {
  currentDesign.value = designNumber
  showPreviewDialog.value = true
}

const selectDesign = async (designNumber: number) => {
  try {
    await ElMessageBox.confirm(
      `确定选择方案${designNumber}：${designInfo[designNumber].title}吗？`,
      '确认选择',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success(`已选择方案${designNumber}：${designInfo[designNumber].title}`)
    
    // 这里可以添加保存用户选择的逻辑
    // 例如：保存到本地存储或发送到服务器
    localStorage.setItem('selectedOrderListDesign', designNumber.toString())
    
    // 跳转到选择的设计方案页面
    router.push(`/express/order-list-design${designNumber}`)
    
  } catch {
    // 用户取消选择
  }
}

const closePreview = () => {
  showPreviewDialog.value = false
}
</script>

<style scoped lang="scss">
.design-preview {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;

  .preview-header {
    text-align: center;
    margin-bottom: 40px;

    h1 {
      font-size: 32px;
      font-weight: 700;
      color: #303133;
      margin-bottom: 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    p {
      font-size: 16px;
      color: #606266;
      margin: 0;
    }
  }

  .design-cards {
    margin-bottom: 40px;

    .design-card {
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      cursor: pointer;
      margin-bottom: 20px;
      overflow: hidden;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
      }

      .design-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px 16px;
        border-bottom: 1px solid #f0f2f5;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }

        .el-tag {
          font-weight: 600;
        }
      }

      .design-preview-image {
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid #f0f2f5;
        position: relative;
        overflow: hidden;

        .preview-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;
          color: white;
          font-size: 16px;
          font-weight: 600;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        &.design1-preview {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.design2-preview {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.design3-preview {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        &.design4-preview {
          background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        img {
          max-width: 100%;
          max-height: 100%;
          object-fit: cover;
        }
      }

      .design-description {
        padding: 20px 24px;

        h4 {
          margin: 0 0 12px 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        ul {
          margin: 0 0 16px 0;
          padding-left: 20px;

          li {
            margin-bottom: 6px;
            color: #606266;
            line-height: 1.5;
          }
        }

        p {
          margin: 0;
          color: #606266;
          line-height: 1.6;
        }
      }

      .design-actions {
        display: flex;
        gap: 12px;
        padding: 16px 24px 20px;
        background: #fafbfc;

        .el-button {
          flex: 1;
          border-radius: 8px;
          font-weight: 500;
        }
      }
    }
  }

  .comparison-table {
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

    h3 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #303133;
    }

    :deep(.el-table) {
      border-radius: 8px;
      overflow: hidden;

      .el-table__header {
        background: #f8f9fa;

        th {
          background: #f8f9fa !important;
          color: #606266;
          font-weight: 600;
          text-align: center;
        }
      }

      .el-table__row {
        &:hover {
          background: #f5f7fa !important;
        }

        td:first-child {
          font-weight: 600;
          color: #303133;
        }
      }
    }
  }

  .preview-content {
    height: 70vh;
    overflow: auto;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .design-cards .el-col {
      margin-bottom: 20px;
    }
  }

  @media (max-width: 768px) {
    padding: 12px;

    .preview-header {
      margin-bottom: 24px;

      h1 {
        font-size: 24px;
      }

      p {
        font-size: 14px;
      }
    }

    .design-cards .design-card {
      .design-actions {
        flex-direction: column;

        .el-button {
          width: 100%;
        }
      }
    }

    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto !important;
    }

    .preview-content {
      height: 60vh;
    }
  }
}
</style>
